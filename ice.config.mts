import { defineConfig } from "@ice/app";
import { PegasusPlugin } from "@ali/build-plugin-pegasus-project";
import spm from "@ali/ice-plugin-spm";
import jsxplus from "@ice/plugin-jsx-plus";
import parse from 'yargs-parser';
import { transformSync } from '@babel/core';

const buildArgvStr = process.env.BUILD_ARGV_STR;
const buildArgv = buildArgvStr ? parse(buildArgvStr) : {};

// The project config, see https://v3.ice.work/docs/guide/basic/config
export default defineConfig({
  // Set your configs here.
  compileDependencies: !!buildArgv.def_publish_version,
  polyfill: 'usage',
  transform: (originalCode, id) => {
    if (id.endsWith('.js') && id.includes('node_modules') && id.includes('inversify-react')) {
      // 借助 babel 编译
      const { code, map } = transformSync(originalCode, {
        plugins: ['@babel/plugin-transform-arrow-functions'],
      });
      return { code, map };
    }
  },
  plugins: [
    PegasusPlugin({
      previewMode: "local",
      documentOnly: true,
    }),
    spm(),
    jsxplus(),
  ],
  externals: {
    '@weex-module/storage': 'commonjs @weex-module/storage',
    '@weex-module/windvane': 'commonjs @weex-module/windvane',
    '@weex-module/dom': 'commonjs @weex-module/dom',
    '@weex-module/mtop': 'commonjs @weex-module/mtop',
    '@weex-module/broadcast': 'commonjs @weex-module/broadcast',
    '@weex-module/event': 'commonjs @weex-module/event',
    '@weex-module/userTrack': 'commonjs @weex-module/userTrack',
    '@weex-module/share': 'commonjs @weex-module/share',
  },
  sourceMap: true,
  server: {
    onDemand: false,
    bundle: true,
    format: "cjs",
    ignores: [{ resourceRegExp: /^(@\/pages)/ }],
  },
  ssr: false,
  ssg: false,
  routes: {
    // 所有二级目录下面的tsx 和 所有ts
    ignoreFiles: ["*/*/**/*.tsx", "**/*.ts"],
  },
  crossOriginLoading: "anonymous",
  dropLogLevel: process.env.ICE_CORE_MODE === "production" ? "error" : "trace",
});
