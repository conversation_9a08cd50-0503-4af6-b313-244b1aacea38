{"name": "@ali/pegasus-project-tbpc-guobu-channel", "version": "1.3.3", "description": "淘宝PC国家补贴频道", "dependencies": {"@ali/alimod-tbpc-venue-buy-ice": "^1.1.6", "@ali/ice-document": "0.1.6", "@ali/ice-plugin-spm": "^3.0.1", "@ali/onex-bi": "1.7.1", "@ali/pcom-tbpc-component-item-cards": "^1.1.9", "@ali/pcom-tbpc-component-tabs": "^1.0.2", "@ali/pcom-tbpc-mod-container": "^1.1.22", "@ali/pcom-tbpc-venue-utils": "^1.0.0", "@ali/wormhole-context": "^1.1.15", "@alife/new-search-suggest": "2.9.0", "@ice/runtime": "^1.4.10", "antd": "^5.24.8", "react-error-boundary": "^5.0.0"}, "devDependencies": {"@ali/build-plugin-pegasus-project": "^2.0.1", "@ali/eslint-config-att": "^1.0.0", "@ali/ice-plugin-def": "^1.0.0", "@ali/ice-plugin-spm": "^3.0.1", "@ali/pegasus-document": "^4.0.0", "@ali/wormhole-sdk": "^1.1.71", "@applint/spec": "^1.2.3", "@ice/app": "^3.4.0", "@ice/plugin-jsx-plus": "^1.0.4", "@types/node": "^18.11.17", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "eslint": "^8.35.0", "react": "^18.2.0", "react-dom": "^18.2.0", "stylelint": "^15.2.0", "typescript": "^4.9.5"}, "scripts": {"start": "ice start --speedup", "build": "ice build", "eslint": "eslint ./src --cache --ext .js,.jsx,.ts,.tsx", "eslint:fix": "npm run eslint -- --fix", "stylelint": "stylelint \"src/**/*.{css,scss,less}\" --cache", "stylelint:fix": "npm run stylelint -- --fix"}, "publishConfig": {"registry": "https://registry.npm.alibaba-inc.com"}, "repository": "**************:ice-lab/react-materials.git"}