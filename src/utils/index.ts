/**
* 是否预发或本地调试环境，否则为生产环境
*/
export const isPre =
  window.location.hostname.indexOf('pre-wormhole.') >= 0 ||
  window.location.hostname.indexOf('localhost') >= 0;

export function getSchemaDataConfig(cfgName = "searchConfig") {
  let cfgObjects: any[] = [];

  // 递归函数用于遍历对象
  function traverse(obj: Record<string, any>) {
    for (let key in obj) {
      if (obj.hasOwnProperty(key)) {
        if (key === cfgName) {
          cfgObjects.push(obj[key]);
        } else if (typeof obj[key] === "object" && obj[key] !== null) {
          // 如果当前值是对象，则继续遍历
          traverse(obj[key]);
        }
      }
    }
  }
  // 从顶层对象开始遍历
  traverse(window?.$data?.page || window.$data.mock);
  return cfgObjects;
}

export function getTrackInfo(string) {
  try {
    const { trackInfo } = JSON.parse(string);
    return genParamsByTrackInfo(trackInfo);
  } catch (err) {
    return {};
  }
}

function genParamsByTrackInfo(trackInfo: string) {
  try {
    return trackInfo
      .split('-----')[1]
      .split('&')
      .reduce((prev, cur) => {
        const [key, value] = cur.split('=');
        prev[key] = value;
        return prev;
      }, {});
  } catch (err) {
    return {};
  }
}

export const generateTtid = () => {
  try {
      const channel = 1;
      const appName = "tbwang";
      let osType = "unknown";
      const appVersion = "1.0.0";
      const source = "pc";
      const platform = window.navigator.platform;
      if (/Mac/.test(platform)) {
          osType = "mac";
      } else if (/Win/.test(platform)) {
          osType = "windows";
      } else if (/Linux/.test(platform)) {
          osType = "linux";
      }
      return `${channel}@${appName}_${osType}_${appVersion}#${source}`;
  } catch (e) {
      return "1@tbwang_unknown_1.0.0#pc";
  }
};

export const spmA = 'gov-subsidy-app'; 
export const spmB = isPre ? "home-pc-pre" : "home-pc";

/**
 * 拼接品牌名和商品标题
 * 如果标题中已包含分隔符，则直接用空格连接
 * 否则使用分隔符连接
 * @param brandName 品牌名
 * @param title 商品标题
 * @param separator 分隔符，默认为"丨"
 * @returns 拼接后的标题
 */
export const formatItemTitle = (brandName?: string, title?: string, separator: string = '丨'): string => {
  if (!brandName) return title || '';
  if (!title) return brandName || '';
  
  // 检查标题中是否已包含分隔符
  if (title.includes(separator)) {
    return `${brandName}${title}`;
  }
  
  return `${brandName}${separator}${title}`;
};

export function isLogin() {
  return window?.lib?.login?.isLogin();
}