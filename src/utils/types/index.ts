export interface IFetchAldDataParams {
  appId: string;
  pageSize?: number;
  pageIndex?: number;
  resId?: string;
  count?: number;
  bizId?: string;
  extraParams?: {
    [key: string]: any;
  }
}

export interface IFetchAldDataResult <T>{
  msgCode: string;
  msgInfo: string;
  success: boolean;
  currentPage: number;
  pageSize: number;
  totalPage: number;
  trackParams: Record<string, any>; 
  resId: string;
  total: number;
  hasMore: boolean;
  data: any[];
}

/**
 * 排行榜详细信息接口
 */

/**
 * 跟踪参数信息接口
 */
interface RankListTrackParams {
  /**
   * 跟踪信息
   */
  trackInfo: string;
  /**
   * 点击跟踪信息
   */
  clickTrackInfo: string;
}

export interface RankListDetail {
  /**
   * 排行榜ID
   */
  id: string;
  /**
   * 排行榜内部ID
   */
  rankId: string;
  /**
   * 排行榜类型编号
   */
  rankType: number;
  /**
   * 排行榜标题
   */
  title: string;
  /**
   * 推荐类型
   */
  recType: string;
  /**
   * 实体类型
   */
  entityType: string;
  /**
   * 是否需要展示前N名
   */
  isNeedTopN: boolean;
  /**
   * 点击跳转链接
   */
  clickUrl: string;
  /**
   * 跟踪参数信息
   */
  trackparams: RankListTrackParams;
  /**
   * 排行榜详情页面链接
   */
  detailUrl: string;
  /**
   * 简短热销描述
   */
  shortHotDesc: string;
  /**
   * 热销描述
   */
  hotDesc: string;
  /**
   * 排行榜中的商品列表
   */
  itemList: RankListItem[];
  /**
   * 可选的排序类型列表
   */
  sortTypeList: string[];
  /**
   * 当前使用的排序类型
   */
  sortType: string;
  /**
   * 分类名称
   */
  kindName: string;
}

/**
 * 排行榜类型描述接口
 */
interface RankListItemRankType {
  /**
   * 排行榜类型编号
   */
  rankType: number;
  /**
   * 排行榜类型描述
   */
  rankTypeDesc: string;
}

/**
 * 排行榜响应数据接口
 */
export interface IRankListResponseData {
  /**
   * 排行榜列表
   */
  rankList: RankListDetail[];
  /**
   * 当前排行榜类型编号
   */
  rankType: number;
  /**
   * 是否还有更多数据
   */
  hasMore: string;
  /**
   * 排行榜类型列表
   */
  rankTypeList: RankListItemRankType[];
  /**
   * 位置信息
   */
  __pos__: number;
  /**
   * 跟踪信息
   */
  __track__: string;
}

/**
 * 排行榜列表中的单项商品信息接口
 */
export interface RankListItem {
  /**
   * 实体类型，此处固定为'ITEM'
   */
  entityType: "ITEM";
  /**
   * 商品唯一标识ID
   */
  id: string;
  /**
   * 商品ItemID
   */
  itemId: string;
  /**
   * 活动交易计数（字符串形式）
   */
  activityDealCount: string;
  /**
   * 活动IP访问量（字符串形式）
   */
  activityIpv: string;
  /**
   * 透明背景图片URL
   */
  transparentPicUrl: string;
  /**
   * 商品标题
   */
  title: string;
  /**
   * 商品详情页链接
   */
  url: string;
  /**
   * 移动端最终价格（字符串形式）
   */
  wapFinalPrice: string;
  /**
   * 商品福利列表（字符串数组）
   */
  benefits: string[];
  /**
   * 特色描述1
   */
  feature1: string;
  /**
   * 商品图片URL
   */
  picUrl: string;
  /**
   * 排名变化描述
   */
  profitDesc: string;
}

export interface IResultOthers {
  resultSuccess: boolean;
  resultRetCode: string;
  resultMessage: string;
}

// 请求类型ald 还是 ChannelDynamic
declare const RequestTypes: [
  "dynamic",
  "ald"
]
export type RequestType = (typeof RequestTypes)[number];