export interface BaseItem {
  id: string;
  title: string;
  [key: string]: any;
}

export interface TabData<T = BaseItem> {
  items: T[];
  floatContents?: any[];
  bizFilters?: any[];
  hasMore?: boolean;
  [key: string]: any;
}

export interface TabState<T = BaseItem> {
  data: TabData<T> | null;
  pageNo: number;
  items: T[];
  hasMore: boolean;
  initialized: boolean;
  loading: boolean;
  floatContents?: any[];
  bizFilters?: any[];
  error?: Error | null;
  addressId?: number | string; // 添加地址ID字段
  secondaryTabId?: string | string[]; // 添加二级标签页ID字段，支持多选
}

export interface MtopParams {
  bizId: number;
  resId: string;
  divisionCode: string;
  isOversea: boolean;
  districtDivisionCode: string;
  addressId: number;
  chaoshiZoneId: string;
  cdnOnly: boolean;
  itemIds: string;
  useNewCardConfig: number;
  pvuuid: string;
  needNav: boolean;
  pageSize: number;
  category: string;
  bizFilter: string;
  abConfigJsonStr: string;
  pageNo: number;
  fromSource: string;
  urlParams: string;
  fromItemId: string;
}

export interface FetchParams {
  appId?: string;
  params: MtopParams | string;
}

export interface MtopResponse<T> {
  resultValue?: {
    [key: string]: {
      data: TabData<T>;
    };
  };
}

export interface UseHomeDataReturn<T = BaseItem> {
  data: TabData<T> | null;
  loading: boolean;
  error: Error | null;
  hasMore: boolean;
  currentTabState: TabState<T> | null;
  fetchTabData: (tabId: string, pageNo?: number, forceRefresh?: boolean, needFloatContents?: boolean) => Promise<void>;
  switchTab: (tabId: string) => void;
  refresh: (tabId: string, newSecondaryTabId?: string | string[]) => Promise<void>;
  loadMore: (tabId: string) => void;
  retry: () => void;
  setLoading: (loading: boolean) => void;
  cancelCurrentRequest: () => void;
}
