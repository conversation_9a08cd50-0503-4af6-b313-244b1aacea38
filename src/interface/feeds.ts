// Base Item interface
export interface Item {
  id: string;
  whiteImg?: string;
  imageUrl?: string;
  title?: string;
  price?: number;
  brandName?: string;
  url?: string;
  tags?: any[];
  priceBenefits?: string;
  priceTxtEntity?: {
    text?: string;
    preText?: string;
  };
  rankInfo?: {
    rankNo: string;
    rankTitle: string;
    rankLink: string;
  };
  [key: string]: any;
}

// Feeds component props
export interface FeedsProps {
  curId: string;
  currentItemslist: Item[];
  benefitDefaultText: string;
  loading: boolean;
  onRefresh: () => void;
  onLoadMore: () => void;
  hasMore: boolean;
  divisionCode?: string | undefined;
}

// ProductGrid component props
export interface ProductGridProps {
  items: Item[];
  oneModCols: number;
  benefitDefaultText: string;
  hasMore: boolean;
  divisionCode: string | undefined;
}

// ProductCard component props
export interface ProductCardProps {
  item: Item;
  benefitDefaultText: string;
  divisionCode: string | undefined;
}

// EmptyState component props
export interface EmptyStateProps {
  onRefresh: () => void;
}

// Skeleton component props
export interface SkeletonProps {
  oneModCols: number;
  rows?: number;
}
