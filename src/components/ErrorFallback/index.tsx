import styles from './index.module.less';

interface ErrorFallbackProps {
  onRetry?: () => void;
}

const ErrorFallback = ({ onRetry }: ErrorFallbackProps) => {
  return (
    <div className={styles.errorContainer}>
      <img 
        src="https://img.alicdn.com/imgextra/i2/O1CN01cTzjzt1txOhlxGFQs_!!6000000005968-2-tps-280-172.png" 
        className={styles.errorImage} 
      />
      <div className={styles.errorText}>活动太火爆了，请刷新或稍后重试</div>
      <button 
        onClick={onRetry}
        className={styles.retryButton}
      >
        刷新
      </button>
    </div>
  );
};

export default ErrorFallback;
