import { useMemo } from "react";
import styles from "./index.module.less";
import { getSchemaDataConfig } from "../../utils";
import { ExpWrap } from "@ali/onex-bi/react";

export default () => {
  const { headerResImg, headerResUrl} = useMemo(() => {
    const config = getSchemaDataConfig('resConfig');
    return config?.[0] || {};
  }, []);
  if (!headerResUrl || !headerResImg) return <div className={styles.empty} />;
  return (
    <ExpWrap
      elementTag="a"
      modId="header-banner-res"
      modName="头部--右侧资源位"
      className={styles.resWrap}
      href={headerResUrl}
      target="_blank"
      data-spm="header-banner-res"
    >
      <img src={headerResImg} alt="国补活动" className={styles.resImg} />
    </ExpWrap>
  );
}