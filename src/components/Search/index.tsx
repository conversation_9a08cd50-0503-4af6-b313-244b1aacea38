import { useEffect, useMemo, useRef, useState } from "react";
import styles from "./index.module.less";
import SearchSuggest from "@alife/new-search-suggest";
import "@alife/new-search-suggest/build/bundle.css";
import { getSchemaDataConfig, isPre, spmA, spmB } from "../../utils";
import { ExpWrap } from "@ali/onex-bi/react";

export default function Search() {
  const { icString, shadingWordsList } = useMemo(() => {
    const { icString = 'service:gjbt' } = getSchemaDataConfig('searchIcConfig')?.[0] || {};
    const shadingWordsList = getSchemaDataConfig('shadingWordsList')?.[0] || [];
    return { icString, shadingWordsList};
  }, []);
  const formButtonDom = useRef(null);
  const [onFocus, setOnFocus] = useState(false);
  const [initFinished, setInitFinished] = useState(false); // 搜索框是否初始化完成
  const [suggestBoxShow, setSuggestBoxShow] = useState(false); // 搜索框是否展示
  // 焦点模式
  // const isFocusMode = useMemo(() => {
  //   return suggestBoxShow || onFocus;
  // }, [suggestBoxShow, onFocus]);

  let icString_final = icString;
  try {
    icString_final = icString?.trim(); // 去除空格
  } catch (error) {}


  const initSearch = () => {
    const fromSource = 'gjbt';
    const spm = [spmA, spmB, fromSource, 'search'].join('.');
    const spmStr = `spm=${spm}`;
    const searchSuggest = new SearchSuggest("#J_Search", {
      target: "_blank", // 跳转方式【优先级最高】
      isProd: !isPre, // 是否是生产环境
      imgSearchSpm: spm, // 图片搜索跳转兜底spm
      imageSearchForbidden: false, // 禁止图片搜索
      stopSearchImageJump: false, // 禁止图片搜索跳转
      disableImgSearchTip: false, // 不提示新人提示
      imgSearchJumpDataMode: "postMessage", // 图搜数据跨域方案: postMessage | cdn
      imgSearchTipText: "",
      ttid: "1@tbwang_unknown_1.0.0#pc", // 【必传】ttid
      // renderGuessWant: ['item'], // 配置展示“猜你想搜”模块的tab的value，默认为item
      renderGuessWant: [],
      referer: "pc_taobao",
      fromSource,
      combobox: {
        popupClassName: "home-search-popup",
        value: "",
        autoTrim: false,
      },
      styleConfig: {
        borderColorFix: true, // 是否固定边框颜色
      },
      // required: true, // false
      tab: {
        data: [
          {
            value: "item",
            label: "宝贝",
            action: `//s.taobao.com/search?channelSrp=guobu_channel&${icString_final ? `boxFilterList=${icString_final || ""}&` : ""}${spmStr || ""}`,
            selected: true,
          },
        ],
      },
      plugins: [
        new SearchSuggest.RecommendPlugin({
          // enableItemValueList: ['item'],
          disableAutoCallApi: true,
          // value: searchPlaceholder || '',
          shadingWordsList: shadingWordsList.map(({ searchText, displayText, tagIconText, tagIconUrl }) => ({
            searchText: searchText || displayText,
            displayText,
            tagBGColor: 'rgb(14, 190, 87)',
            tagIconText: tagIconText || '国家补贴',
            tagIconUrl: tagIconUrl || 'https://img.alicdn.com/imgextra/i4/O1CN01aY8ScF292B9HPzCmN_!!6000000008009-2-tps-24-24.png',
            iconExtraInfo: {
              srpFilterParam: icString
            },
          }))
        }),
      ],
      autoRedirect: true,
      sourceUrl: "https://suggest.taobao.com/sug?k=1&area=c2c",
    });
    searchSuggest.on("visibleChange", (data) => {
      setSuggestBoxShow && setSuggestBoxShow(data?.visible);
    });
    if (searchSuggest) {
      setInitFinished && setInitFinished(true);
    }
  };

  useEffect(() => {
    initSearch();
  }, []);

  return (
    <ExpWrap
      modId="search"
      modName="搜索-搜索组件"
      className={`wrapper ${styles.searchWrapper}`}
      style={{
        visibility: initFinished ? "visible" : "hidden",
      }}
    >
      <div
        id="J_Search"
        role="search"
        className={`search-suggest`}
        data-spm="searchbar"
        style={{
          width: "100%",
        }}
      >
        <form
          data-sg-type="form"
          target="_blank"
          action="//s.taobao.com/search"
        >
          <div className="search-suggest-split" />
          <ExpWrap
            modId="search.btn"
            modName="搜索-搜索按钮"
          >
            <button
              ref={formButtonDom}
              className="btn-search"
              type="submit"
            >
              搜索
            </button>
          </ExpWrap>
          <div data-sg-type="placeholder" />
          <ExpWrap
            modId="search.input"
            modName="搜索-搜索输入框"
            data-sg-type="combobox"
            className="search-suggest-combobox"
          >
            <input
              id="q"
              name="q"
              aria-label=""
              accessKey="s"
              autoComplete="off"
              onFocus={() => {
                setOnFocus && setOnFocus(true);
              }}
              onBlur={() => {
                setOnFocus && setOnFocus(false);
              }}
            />
          </ExpWrap>
        </form>
      </div>
    </ExpWrap>
  );
}
