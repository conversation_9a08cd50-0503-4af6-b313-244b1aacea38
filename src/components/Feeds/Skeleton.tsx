import { AdaptRow, AdaptCol, LayoutRadioEnum } from "@ali/pcom-tbpc-mod-container";
import styles from './index.module.less';
import { SkeletonProps } from '@/interface/feeds';

const Skeleton = ({ oneModCols, rows = 2 }: SkeletonProps) => {
  // 确保生成完整行数的骨架
  const totalItems = oneModCols * rows;
  const items = Array.from({ length: totalItems });

  return (
    <AdaptRow wrap className={styles.skeletonWrap}>
      {items.map((_, index) => (
        <AdaptCol
          span={LayoutRadioEnum.entire_row / oneModCols}
          key={`skeleton_${index}`}
        >
          <div className={styles.skeletonItem}>
            <div className={styles.skeletonImage} />
            <div className={styles.skeletonContent}>
              <div className={styles.skeletonTitle} />
              <div className={styles.skeletonPrice} />
            </div>
          </div>
        </AdaptCol>
      ))}
    </AdaptRow>
  );
};

export default Skeleton;
