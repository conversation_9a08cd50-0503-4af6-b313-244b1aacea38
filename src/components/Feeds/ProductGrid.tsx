import { AdaptRow, AdaptCol, LayoutRadioEnum } from "@ali/pcom-tbpc-mod-container";
import ProductCard from './ProductCard';
import { ProductGridProps } from '@/interface/feeds';

const ProductGrid = ({ items, divisionCode, oneModCols, benefitDefaultText, hasMore }: ProductGridProps) => {
  // Calculate how many complete rows we can make
  const completeRowCount = Math.floor(items.length / oneModCols);
  // Get items for complete rows
  const completeRowItems = items.slice(0, completeRowCount * oneModCols);
  // Get remaining items (for last row)
  const remainingItems = items.slice(completeRowCount * oneModCols);

  return (
    <>
      <AdaptRow wrap>
        {/* Render complete rows */}
        {completeRowItems.map((item, index) => (
          <AdaptCol
            span={LayoutRadioEnum.entire_row / oneModCols}
            key={`col_${index}_${item.id}`}
            style={{ marginBottom: "16px" }}
          >
            <ProductCard
              item={item}
              benefitDefaultText={benefitDefaultText}
              divisionCode={divisionCode}
            />
          </AdaptCol>
        ))}

      </AdaptRow>
      {/* Only render remaining items if there's no more data coming */}
      {!hasMore && remainingItems.length > 0 && (
        <AdaptRow wrap style={{ width: '100%' }}>
          {remainingItems.map((item, index) => (
            <AdaptCol
              span={LayoutRadioEnum.entire_row / oneModCols}
              key={`col_remaining_${index}_${item.id}`}
              style={{ marginBottom: "16px" }}
            >
              <ProductCard
                item={item}
                benefitDefaultText={benefitDefaultText}
                divisionCode={divisionCode}
              />
            </AdaptCol>
          ))}
        </AdaptRow>
      )}
    </>
  );
};

export default ProductGrid;
