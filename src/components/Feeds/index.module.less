.container {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}

.guoBuContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  width: 101%;
}

.emptyContainer {
  display: flex;
  margin: auto;
  width: 326px;
  min-height: 500px;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;

  .emptyContent {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .emptyIcon {
      width: 98px;
      height: 66px;
      background: url(https://img.alicdn.com/imgextra/i1/O1CN01pHfOBO1nNmD8frHBn_!!6000000005078-2-tps-197-134.png) no-repeat;
      background-size: 100% 100%;
    }

    .emptyTip {
      width: 100%;
      display: flex;
      flex-direction: row;
      justify-content: center;
      color: #999;
      font-size: 14px;
      margin: 4px auto 12px;
    }

    .refreshButton {
      display: flex;
      width: 92px;
      padding: 6px 0;
      justify-content: center;
      border: 1px solid #ebebeb;
      border-radius: 8px;
      align-items: center;
      cursor: pointer;

      &:hover {
        opacity: 0.8;
      }

      .refreshIcon {
        width: 18px;
        height: 18px;
        background: url(https://img.alicdn.com/imgextra/i3/O1CN01VLfdSW1iHIWxEvh4d_!!6000000004387-2-tps-37-35.png) no-repeat;
        background-size: 100% 100%;
      }

      .refreshText {
        color: #1f1f1f;
        font-size: 16px;
        margin-left: 8px;
      }
    }
  }
}

.skeletonWrap {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  
  .skeletonItem {
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    height: 280px;
    margin: 0 0 16px;  // 添加底部间距，匹配ProductGrid的样式

    .skeletonImage {
      width: 100%;
      height: 180px;
      background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%);
      background-size: 400% 100%;
      animation: skeleton-loading 1.4s ease infinite;
    }

    .skeletonContent {
      padding: 12px;

      .skeletonTitle {
        width: 85%;
        height: 16px;
        background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%);
        background-size: 400% 100%;
        animation: skeleton-loading 1.4s ease infinite;
        margin-bottom: 12px;
        border-radius: 4px;
      }

      .skeletonPrice {
        width: 40%;
        height: 24px;
        background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%);
        background-size: 400% 100%;
        animation: skeleton-loading 1.4s ease infinite;
        border-radius: 4px;
      }
    }
  }
}

@keyframes skeleton-loading {
  0% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0 50%;
  }
}

:global(.cardImgWrap) {
  &::after {
    background-color: rgba(0, 0, 0, 0.03);
    border-radius: 12px;
  }
}

.contentWrapper {
  position: relative;
  width: 100%;
}

.skeletonWrapper {
  transition: opacity 0.3s ease;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1;
}

.contentContainer {
  transition: opacity 0.3s ease, min-height 0.3s ease;
  position: relative;
  z-index: 2;
}

.bottomContent {
  width: 100%;
}

.noMore {
  text-align: center;
  color: #999;
  font-size: 14px;
  padding: 16px 0;
}

.priceRightSlot {
  color: #7a7a7a;
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  margin-left: 6px;
}
