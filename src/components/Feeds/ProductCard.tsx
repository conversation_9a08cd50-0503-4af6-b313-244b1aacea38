import GuoBuItemCard, { ECardTypeEnum} from "@ali/pcom-tbpc-component-item-cards";
import { ProductCardProps } from '@/interface/feeds';
import styles from './index.module.less';
import { ExpWrap } from "@ali/onex-bi/react";
import { useMemo } from "react";
import { getSchemaDataConfig, getTrackInfo, formatItemTitle } from "@/utils";


const ProductCard = ({ item, benefitDefaultText, divisionCode }: ProductCardProps) => {
  const rankBenefit = item?.rankInfo?.rankNo &&
    item?.rankInfo?.rankTitle &&
    item?.rankInfo?.rankTitle.length < 11
      ? `${item?.rankInfo?.rankTitle}榜·第${item?.rankInfo?.rankNo}名`
      : '';

  const benefits = useMemo(() => {
    if (rankBenefit) {
      return [rankBenefit];
    }

    if (!item?.pcItemBenefit?.length) {
      const { defaultBenefit = '火爆热卖中' } = getSchemaDataConfig('funcConfig')?.[0] || {};
      return [defaultBenefit];
    }

    const zfljBenefits = item?.pcItemBenefit?.filter(benefit =>
      // benefit.subToolType === 'zflj' ||  benefit.subToolType === 'zfbtAddr'
      // 这里不再过滤，由产品 @乐诗 在氛围中心控制
      true
    );

    return (zfljBenefits?.length > 0 
      ? zfljBenefits 
      : item?.pcItemBenefit
    )?.map(item => item) || [benefitDefaultText];
  }, [item]);

  return (
    <ExpWrap
      modId={`feeds-item`}
      modName={`商品卡`}
      expParams={{
        itemIndex: item.itemIndex,
        index: item.itemIndex,
        itemType: 'normal',
        itemId: item.id,
        itemTitle: item?.title,
        priceBenefits: benefits?.join(','),
        itemPrice: item?.pcDiscountPrice,        
        imgSource: item?.mainPictUrl || item.imageUrl,
        divisionCode,
        ...getTrackInfo(item.trackParams), // trackParams 在feeds 接口里面会返回, 这里是给算法分析数据用的
        __traceId: '',
      }}
    >
      <GuoBuItemCard
        type={ECardTypeEnum.NORMAL_GOODS_CARD}
        data={{
          itemId: item.id,
          index: Number(item.id),
          mainTitle: formatItemTitle(item?.brandDisplayName, item?.title),
          picUrl: item?.mainPictUrl || item.imageUrl || '',
          itemUrl: item?.url,
          benefits,
          titleIcon: item?.actIcon || '',
          priceStr: item?.pcDiscountPrice,
          benefitsHighLight: rankBenefit ? '#B88449' : '#FF5000',
          themeColor: '#ff5000',
          priceRightSlot: (
            <span className={styles.priceRightSlot}>
              {String(item?.pcDiscountPriceDisplay?.type) === '99' && item?.pcDiscountPriceDisplay?.desc
                ? item?.pcDiscountPriceDisplay?.desc
                : ''
              } 
            </span>
          ),
        }}
      />
    </ExpWrap>
  );
};

export default ProductCard;
