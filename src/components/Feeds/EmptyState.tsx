import styles from './index.module.less';
import { EmptyStateProps } from '@/interface/feeds';

const EmptyState = ({ onRefresh }: EmptyStateProps) => {
  return (
    <div className={styles.emptyContainer}>
      <div className={styles.emptyContent}>
        <div className={styles.emptyIcon} />
        <div className={styles.emptyTip}>
          当前没有更多商品了，试试刷新或去其他页面看看吧
        </div>
        <div className={styles.refreshButton} onClick={onRefresh}>
          <div className={styles.refreshIcon} />
          <div className={styles.refreshText}>
            刷新
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmptyState;
