import { useRef, useState, useEffect } from 'react';
import { useInfiniteScroll } from '@/hooks/useInfiniteScroll';
import { useRowCols, LayoutRadioEnum } from '@ali/pcom-tbpc-mod-container';
import styles from './index.module.less';
import ProductGrid from './ProductGrid';
import EmptyState from './EmptyState';
import Skeleton from './Skeleton';
import { FeedsProps } from '@/interface/feeds';

export default function Feeds({ 
  curId, 
  currentItemslist, 
  benefitDefaultText,
  loading,
  onRefresh,
  onLoadMore,
  hasMore,
  divisionCode,
}: FeedsProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const oneModCols = useRowCols(LayoutRadioEnum.entire_row);
  // 添加状态来控制骨架屏的显示
  const [showSkeleton, setShowSkeleton] = useState(true);
  // 添加状态来跟踪是否已经有足够的数据
  const [hasEnoughData, setHasEnoughData] = useState(false);

  useInfiniteScroll(containerRef, () => {
    if (hasMore && !loading) {
      onLoadMore();
    }
  }, {
    threshold: 800, // 提高阈值，提前加载
    disabled: !hasMore || loading
  });

  // 监控数据变化，决定是否显示骨架屏
  useEffect(() => {
    if (!currentItemslist) {
      setShowSkeleton(true);
      setHasEnoughData(false);
      return;
    }

    // 检查是否有足够的数据显示当前内容加下一次加载的两行
    const minItemsForCurrentDisplay = oneModCols ? oneModCols * 2 : 10; // 当前显示至少需要两行
    const minItemsForNextLoad = oneModCols ? oneModCols * 2 : 10; // 下一次加载至少需要两行
    const minItemsNeeded = minItemsForCurrentDisplay + minItemsForNextLoad; // 总共需要的最小数量
    
    // 更新足够数据的判断逻辑 - 是否有足够数据显示下一次的两行
    if (currentItemslist.length >= minItemsNeeded) {
      setHasEnoughData(true);
    } else {
      setHasEnoughData(false);
    }
    
    // 决定是否显示骨架屏
    if (currentItemslist.length >= minItemsForCurrentDisplay) {
      // 有足够数据显示当前内容，可以隐藏骨架屏
      setTimeout(() => setShowSkeleton(false), 100);
    } else if (!loading && !hasMore) {
      // 没有足够数据，但已经没有更多数据了，也隐藏骨架屏
      setTimeout(() => setShowSkeleton(false), 100);
    } else {
      // 没有足够数据，且还在加载或有更多数据，保持骨架屏显示
      setShowSkeleton(true);
    }
  }, [currentItemslist, loading, hasMore, oneModCols]);

  if (!oneModCols) return null;

  const validItems = currentItemslist?.filter(item => 
    item?.id && (item?.whiteImg || item?.imageUrl)
  ).map((item, index) => ({
    ...item,
    itemIndex: index + 1,
  }));

  const hasValidItems = validItems?.length > 0;

  // 计算两行骨架屏的高度
  const skeletonHeight = 280 * 2 + 16; // 骨架屏每行高度280px + 底部间距16px
  
  return (
    <div
      className={`${styles.container} ${styles.guoBuContainer}`}
      ref={containerRef}
      style={{ width: '100%', minHeight: '200px' }}
    >
      {/* 使用CSS过渡效果控制骨架屏和实际内容的显示 */}
      <div className={styles.contentWrapper}>
        {/* 初始骨架屏 - 使用opacity控制显示/隐藏 */}
        <div className={styles.skeletonWrapper} style={{ 
          opacity: showSkeleton ? 1 : 0, 
          visibility: showSkeleton ? 'visible' : 'hidden',
          height: `${skeletonHeight}px`
        }}>
          <Skeleton oneModCols={oneModCols} rows={2} />
        </div>
        
        {/* 实际内容 - 始终渲染，使用opacity控制显示/隐藏 */}
        <div className={styles.contentContainer} style={{ 
          opacity: !showSkeleton && hasValidItems ? 1 : 0,
          minHeight: showSkeleton ? `${skeletonHeight}px` : 'auto' // 确保初始高度与骨架屏相同
        }}>
          {hasValidItems && (
            <ProductGrid 
              items={validItems}
              oneModCols={oneModCols}
              benefitDefaultText={benefitDefaultText}
              hasMore={hasMore}
              divisionCode={divisionCode}
            />
          )}
        </div>
      </div>

      {/* 底部内容 */}
      <div className={styles.bottomContent}>
        {!loading && !hasValidItems && !hasMore && <EmptyState onRefresh={onRefresh} />}
        
        {/* 底部骨架屏 - 使用CSS过渡效果控制显示/隐藏 */}
        <div className={styles.bottomSkeleton} style={{ 
          opacity: loading && hasValidItems && hasEnoughData ? 1 : 0,
          height: loading && hasValidItems && hasEnoughData ? '560px' : 0, // 使用固定高度
          overflow: 'hidden',
        }}>
          <Skeleton oneModCols={oneModCols} rows={2} />
        </div>
        
        {!loading && !hasMore && hasValidItems && (
          <div className={styles.noMore}>没有更多了</div>
        )}
      </div>
    </div>
  );
}
