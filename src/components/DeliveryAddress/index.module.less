/* stylelint-disable color-hex-case */
.container {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-items: center;
  width: 256px;
  height: 80px;
}

.wrapper {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-items: center;
  width: 254px;
  height: 80px;
}

.gbImage {
  width: 130px;
  height: 32px;
}

.infoContainer {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  width: 108px;
  height: 48px;
  margin-left: 16px;
}

.infoWrapper {
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
  height: 48px;
  cursor: pointer;
}

.locationWrapper {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-items: center;
  height: 24px;
}

.locationIcon {
  width: 16px;
  height: 16px;
}

.locationText {
  font-size: 16px;
  line-height: 24px;
  color: #03A848;
  margin-left: 2px;
  font-weight: 500;
}

.zoneWrapper {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-items: center;
  height: 24px;
  margin-top: 0;
  cursor: pointer;
}

.zoneText {
  font-size: 16px;
  line-height: 24px;
  color: #03A848;
  font-weight: 500;
}

.arrowIcon {
  width: 12px;
  height: 12px;
  margin-left: 4px;
  transform: rotate(180deg);
  &.arrowIconActive {
    transform: rotate(0deg);
  }
}

.addressWrap {
  width: 688px;
  max-height: 208px;
  overflow: auto;
  display: flex;
  flex-wrap: wrap;
  .title {
    font-size: 20px;
    font-weight: 500;
    margin-bottom: 20px;
    color: #333;
  }
}
