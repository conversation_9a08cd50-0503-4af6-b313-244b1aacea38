import React, { useCallback, useMemo, useState } from 'react';
import styles from './index.module.less';
import { Popover } from 'antd';
import useDeliveryAddress, { getTargetAddressInfo } from '@/hooks/useDeliveryAddress';
import AddressCard from '../AddressCard';
import { ExpWrap } from '@ali/onex-bi/react'
interface AddressProps {
  onSelect: (curAddress: IDeliveryAddress) => void;
}

const DeliveryAddress: React.FC<AddressProps> = ({ onSelect }) => {
  const [selectedAddress, setSelectedAddress] = useState<IDeliveryAddress>();
  const [isPopoverVisible, setPopoverVisible] = useState(false);
  const handleAddressClick = useCallback((address: IDeliveryAddress) => {
    // 切换地址后，需要再次获取猫超的zoneId，便于价格补全
    getTargetAddressInfo(address.divisionCode).then(() => {
      setSelectedAddress(address);
      onSelect(address);
    });
  }, []);
  const addressList = useDeliveryAddress(handleAddressClick);
  const selProvince = useMemo(() => {
    if (selectedAddress?.province) {
      const province = selectedAddress.province;
      if (province.length >= 3) {
        if (['内蒙古', '黑龙江'].some((item) => province.includes(item))) {
          return province.slice(0, 3);
        } else { // 其他自治区、特别行政区等
          return province.slice(0, 2);
        }
      }
      return province;
    }
    return '选择地址';
  }, [selectedAddress]);
  return (
    <div className={styles.container}>
      <div className={styles.wrapper}>
        <img 
          className={styles.gbImage} 
          src="https://img.alicdn.com/imgextra/i1/O1CN01AhAvwI1BuL1wUYJTx_!!6000000000005-2-tps-260-64.png"
          alt='国家补贴'
        />
        <div className={styles.infoContainer}>
          <Popover
            placement="bottomLeft"
            color='#fff'
            trigger={['hover']}
            title="选择收货地址"
            onOpenChange={(openStatus) => {
              setPopoverVisible(openStatus);
            }}
            popupVisible={isPopoverVisible}
            content={
              <ExpWrap
                modId="header.address-title"
                modName="头部-地址切换组件"
                className={styles.addressWrap}
              >
                {
                  addressList.map((address) => {
                    return (
                      <AddressCard
                        key={address.deliverId}
                        address={address} 
                        isSelected={address.deliverId === selectedAddress?.deliverId} 
                        onClick={() => handleAddressClick(address)} 
                      />
                    );
                  })
                }
              </ExpWrap>
            }>
              <div className={styles.infoWrapper}>
                <div className={styles.locationWrapper}>
                  <img 
                    className={styles.locationIcon} 
                    src="https://img.alicdn.com/imgextra/i3/O1CN01gpeFwJ27pyyhoUnLl_!!6000000007847-2-tps-32-32.png" 
                  />
                  <span className={styles.locationText}>{selProvince}</span>
                </div>
                <div className={styles.zoneWrapper}>
                  <span className={styles.zoneText}>补贴专区</span>
                  <img 
                    className={`${styles.arrowIcon} ${isPopoverVisible ? styles.arrowIconActive : ''}`} 
                    src="https://img.alicdn.com/imgextra/i2/O1CN01JShj1Z29pBWJ53eqC_!!6000000008116-2-tps-24-24.png" 
                  />
                </div>
              </div>
            </Popover>
        </div>
      </div>
    </div>
  );
};

export default DeliveryAddress;
