import { useState, useCallback, Fragment } from "react";
import Tabs from "@ali/pcom-tbpc-component-tabs";
import Affix from "./affix";
import TabsSkeleton from './TabsSkeleton';
import SecondaryTabs from './SecondaryTabs';
import KeyboardArrowLeftIcon from './icons/KeyboardArrowLeft';
import KeyboardArrowRightIcon from './icons/KeyboardArrowRight';
import styles from "./index.module.less";
import { ExpWrap } from "@ali/onex-bi/react";

const normalTabStyleWidthUnderLine = {
  "& .MuiTabs-root": {
    minHeight: 48,
  },
  "& .MuiTabs-indicator": {
    background: "none",
    transition: "none",
  },
  "& .MuiTab-root": {
    minWidth: "32px",
    minHeight: 48,
    fontSize: "16px",
    fontWeight: "normal",
    padding: "0",
    margin: "0px 16px",
    color: "#1f1f1f",
    // 添加第一个 tab 的样式
    "&:first-of-type": {
      marginLeft: 0,
    },
    "&.Mui-selected": {
      fontWeight: "600",
      color: "#0EBE57",
    },
    "&:hover": {
      color: "#0EBE57 !important",
    },
  },
  "& .MuiTabs-scrollButtons": {
    position: "absolute",
    width: "40px",
    height: "100%",
    zIndex: "999",
    opacity: "1",
    "&.Mui-disabled": {
      opacity: "0",
    },
    "& .MuiSvgIcon-root": {
      fontSize: "24px",
      color: "#1f1f1f",
    },
  },
  "& .MuiButtonBase-root.MuiTabScrollButton-root.MuiTabs-scrollButtons": {
    "&:last-of-type": {
      right: 0,
      background: "linear-gradient(to left, #fff 50%, rgba(15, 10, 9, 0.01))"
    }
  }
};

interface CustomTabsProps {
  curId: string;
  tabsList: Array<{
    key: string;
    label: string;
  }>;
  onChange: (key: string) => void;
  loading?: boolean;
  // 二级tab相关props
  curSecondaryId?: string | string[];
  secondaryTabsList?: Array<{
    key: string;
    label: string;
    // 个性化配置
    tabType?: string;
    selectedBgColor?: string;
    unselectedBgColor?: string;
    selectedStyle?: string;
    unselectedStyle?: string;
    extra?: {
      imgWidth?: number;
      selectedBorderColor?: string;
      [key: string]: any;
    };
  }>;
  onSecondaryChange?: (key: string[]) => void; // 只支持多选数组
  secondaryLoading?: boolean;
}

export default function CustomTabs({
  curId,
  tabsList,
  onChange,
  loading,
  curSecondaryId,
  secondaryTabsList,
  onSecondaryChange,
  secondaryLoading,
}: CustomTabsProps) {
  const [affixed, setAffixed] = useState(false);

  const handleChange = (fixed: boolean) => {
    setAffixed(fixed);
  };

  const renderTabList = useCallback(() => {
    if (loading || !tabsList?.length) {
      return <TabsSkeleton />;
    }

    return (
      <Tabs
        activeKey={curId}
        tabs={tabsList}
        tabsPropsConfig={{
          className: styles.bybtTabs,
          sx: normalTabStyleWidthUnderLine,
          style: {
            minHeight: "48px",
            marginBottom: "0",
            backgroundColor: "#fff",
            position: "relative",
          },
          slots: {
            StartScrollButtonIcon: KeyboardArrowLeftIcon,
            EndScrollButtonIcon: KeyboardArrowRightIcon
          }
        }}
        titleWrapper={(props) => (
          <ExpWrap
            modId={`feeds-tab-${props.index + 1}`}
            modName={`类目tab-${props.label}`}
            {...props}
          />
        )}
        onChange={(_, key) => onChange(key)}
      />
    );
  }, [loading, affixed, tabsList, curId]);

  return (
    <Affix offsetTop={0} style={{ width: '100%' }} onChange={handleChange}>
      <div className={affixed ? 'tbpc-layout' : ''}>
        {renderTabList()}
        {secondaryTabsList && secondaryTabsList.length > 0 && (
          <SecondaryTabs
            curSecondaryId={curSecondaryId}
            secondaryTabsList={secondaryTabsList}
            onChange={onSecondaryChange || (() => {})}
            loading={secondaryLoading}

          />
        )}
      </div>
    </Affix>
  );
}
