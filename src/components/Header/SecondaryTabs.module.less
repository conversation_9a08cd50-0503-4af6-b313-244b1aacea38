.secondaryTabsContainer {
  padding: 0px 0 16px 0;
  background-color: #fff;
}

.secondaryTabsList {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  align-items: center;
}

.secondaryTab {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 4px 8px;
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.08);
  background-color: #fff;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 24px;
  height: 24px;

  &:first-child {
    margin-left: 0;
  }

  &:hover {
    border: 1px solid rgba(0, 0, 0, 0.08);
    background-color: #fff;

    .tabLabel {
      color: rgba(0, 0, 0, 0.52);
      font-weight: bold;
    }
  }

  &.active {
    border-color: #0EBE57;
    background-color: #fff;

    .tabLabel {
      color: #0EBE57;
      font-weight: 600;
    }
  }

  // 自定义配置的 tab 样式
  &.customTab {
    // 保持基础的 padding，但移除边框样式

    &:hover {
      .tabLabel {
        // 保持原有文字样式
        color: inherit;
        font-weight: inherit;
      }
    }

    &.active {
      // 自定义 tab 的选中效果不改变边框
      .tabLabel {
        // 保持原有文字样式
        color: inherit;
        font-weight: inherit;
      }
    }
  }
}

.tabLabel {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
  white-space: nowrap;
  transition: color 0.2s ease;
  font-weight: 400;
}

.customTabImage {
  max-width: 100%;
  height: 14px;
  object-fit: contain;
  display: block;
}
