import { useCallback } from 'react';
import { ExpWrap } from '@ali/onex-bi/react';
import styles from './SecondaryTabs.module.less';

interface TabItem {
  key: string;
  label: string;
  // 个性化配置
  tabType?: string;
  selectedBgColor?: string;
  unselectedBgColor?: string;
  selectedStyle?: string;
  unselectedStyle?: string;
  extra?: {
    imgWidth?: number;
    selectedBorderColor?: string;
    [key: string]: any;
  };
}

interface SecondaryTabsProps {
  curSecondaryId?: string | string[];
  secondaryTabsList: Array<TabItem>;
  onChange: (key: string[]) => void; // 只支持多选，返回数组
  loading?: boolean;
}

export default function SecondaryTabs({
  curSecondaryId,
  secondaryTabsList,
  onChange,
  loading,
}: SecondaryTabsProps) {
  const handleTabClick = useCallback((key: string) => {
    // 多选逻辑
    const currentSelected = Array.isArray(curSecondaryId)
      ? curSecondaryId.filter(Boolean)
      : [curSecondaryId].filter(Boolean);

    // 如果已选中，则移除；否则添加
    let newSelected: string[];
    if (currentSelected.includes(key)) {
      // 如果只剩"全部"，不允许取消选择
      if (currentSelected.length === 1 && key === 'all') {
        return;
      }
      newSelected = currentSelected.filter(id => id !== key && id !== undefined) as string[];
      // 如果取消选择后没有任何选中项，则自动选中"全部"
      if (newSelected.length === 0) {
        newSelected = ['all'];
      }
    } else {
      // 如果选择的是"全部"，则清空其他选项
      if (key === 'all') {
        newSelected = ['all'];
      } else {
        // 如果当前包含"全部"，则移除"全部"
        const withoutAll = currentSelected.filter(id => id !== 'all' && id !== undefined) as string[];
        newSelected = [...withoutAll, key];
      }
    }

    onChange(newSelected);
  }, [curSecondaryId, onChange]);

  // 如果正在加载、没有标签列表，或者只有一个标签（只有"全部"选项），则不显示
  if (loading || !secondaryTabsList?.length || secondaryTabsList.length <= 1) {
    return null;
  }

  // 处理当前选中的tab
  const selectedKeys = Array.isArray(curSecondaryId)
    ? curSecondaryId.filter(Boolean)
    : [curSecondaryId].filter(Boolean);

  return (
    <div className={styles.secondaryTabsContainer}>
      <div className={styles.secondaryTabsList}>
        {secondaryTabsList.map((tab, index) => {
          const isSelected = selectedKeys.includes(tab.key);
          const isCustomTab = tab?.selectedStyle;

          // 获取个性化样式
          const customStyle: React.CSSProperties = {};
          if (isCustomTab) {
            // 移除边框
            if (isSelected && tab.selectedBgColor) {
              customStyle.backgroundColor = tab.selectedBgColor;
              customStyle.border = `1px solid ${tab.selectedBgColor}`
            } else if (!isSelected) {
              // 未选中时使用配置的背景色，如果没有配置则使用默认背景色
              customStyle.backgroundColor = tab.unselectedBgColor || '#fff';
            }
          }

          // 获取图片 URL
          const imageUrl = isCustomTab
            ? (isSelected ? tab.selectedStyle : tab.unselectedStyle)
            : undefined;

          return (
            <ExpWrap
              key={tab.key}
              modId={`secondary-tab-${index + 1}`}
              modName={`二级tab-${tab.label}`}
            >
              <div
                className={`${styles.secondaryTab} ${isSelected ? styles.active : ''} ${isCustomTab ? styles.customTab : ''}`}
                style={customStyle}
                onClick={() => handleTabClick(tab.key)}
              >
                {isCustomTab && imageUrl ? (
                  <img
                    src={imageUrl}
                    alt={tab.label}
                    className={styles.customTabImage}
                  />
                ) : (
                  <span className={styles.tabLabel}>{tab.label}</span>
                )}
              </div>
            </ExpWrap>
          );
        })}
      </div>
    </div>
  );
}
