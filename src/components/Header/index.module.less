:global {
  .MuiTabs-root {
    .MuiButtonBase-root.MuiTabScrollButton-root.MuiTabs-scrollButtons {
      width: 24px !important;
      height: 24px !important;
      border-radius: 50%;
      background: #fff !important;
      box-shadow: 0 0 0 2px #fff;
      border: 1px solid #E8E8E8;
      position: relative;
      z-index: 1;
      // 删除 margin-top，改用绝对定位实现居中
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      
      &::after {
        content: '';
        position: absolute;
        width: 40px;
        height: 100%;
        top: 0;
        z-index: -1;
      }

      // 左箭头的渐变遮罩
      &:first-of-type::after {
        right: -20px;
        // background: linear-gradient(to right, #fff 30%, rgba(255, 255, 255, 0));
      }

      // 右箭头的渐变遮罩
      &:last-of-type::after {
        left: -20px;
        // background: linear-gradient(to left, #fff 30%, rgba(255, 255, 255, 0));
      }
      
      &:hover {
        background: #F5F5F5 !important;
      }
      
      &.Mui-disabled {
        opacity: 0 !important;
        visibility: hidden !important;
        position: absolute !important;
        width: 0 !important;
        height: 0 !important;
        padding: 0 !important;
        margin: 0 !important;
      }
    }
  }
}

.bybtTabs {
  position: relative;
  width: 100%;
}

:global(.affix) {
  background-color: #fff;
  width: 100vw !important;
  z-index: 999;
  left: 0;
}

.tabsSkeleton {
  width: 100%;
  min-height: 48px;
  background: #fff;
  padding: 0 16px;
  margin-bottom: 16px;
  margin-left: -16px;
}

.tabsSkeletonInner {
  display: flex;
  align-items: center;
  height: 48px;
  gap: 32px;
}

.tabItem {
  width: 64px;
  height: 20px;
  background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%);
  background-size: 400% 100%;
  animation: skeleton-loading 1.4s ease infinite;
  border-radius: 4px;
}

@keyframes skeleton-loading {
  0% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0 50%;
  }
}
