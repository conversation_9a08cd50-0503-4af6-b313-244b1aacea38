import React from 'react';
import styles from './index.module.less';

interface AddressCardProps {
  address: IDeliveryAddress;
  isSelected: boolean;
  onClick: () => void;
}

const AddressCard: React.FC<AddressCardProps> = ({ address, isSelected, onClick }) => {
  const { province, city, area, town, addressDetail, fullName, mobile } = address;
  
  return (
    <div 
      className={`${styles.card} ${isSelected ? styles.selected : ''}`}
      onClick={onClick}
    >
      <div className={styles.iconWrapper}>
        {isSelected ? (
          <div className={styles.selectedIcon}>
            <div className={styles.dot}></div>
          </div>
        ) : (
          <div className={styles.unselectedIcon}></div>
        )}
      </div>
      <div className={styles.content}>
        <div className={styles.addressLine}>
          {province} {city} {area} {town}
        </div>
        <div className={styles.detailLine}>{addressDetail}</div>
        <div className={styles.contactLine}>
          {fullName} {mobile}
        </div>
      </div>
    </div>
  );
};

export default AddressCard;
