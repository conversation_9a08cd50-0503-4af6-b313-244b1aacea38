import { Meta, Title, <PERSON>s, Main, Scripts } from "ice";
import { Logger } from '@ali/ice-document';
import { http, data as wormholeData, system as wormholeSystem } from '@ali/wormhole-context';
import BrowserCheck from './components/BrowserCheck';
import { AplusScripts } from '@ali/onex-bi/react';

export default function Document() {
  return (
    <html>
      <head>
        <meta charSet="utf-8" />
        <link rel="icon" href="//gw.alicdn.com/imgextra/i4/O1CN01qOI6vB1zaqrBKbyFr_!!6000000006731-73-tps-64-64.ico" />
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"
        />
        <meta name="def-grey" content={`${http?.url?.query.wh_grey === 'true' ? 'true' : 'false'}`} />
        <Title />
        <Meta />
        <Links />
        <AplusScripts />
        <script
          crossOrigin="anonymous"
          src="//g.alicdn.com/??mtb/lib-promise/3.1.3/polyfillB.js,mtb/lib-mtop/2.7.0/mtop.js,mtb/lib-env/3.0.0/index.min.js,mtb/lib-login/3.6.9/login.js"
        />
        <script src='//o.alicdn.com/tbpc/securitySDK/securitySDK.umd.js'></script>
      </head>
      <body>
        <script
          dangerouslySetInnerHTML={{
            __html: `
              window.$data = ${JSON.stringify(wormholeData)}
              window.$system = ${JSON.stringify(wormholeSystem)};
            `,
          }}
        />
        <BrowserCheck />
        <div className="site-nav" id="J_SiteNav"></div>
        <Main />
        <script dangerouslySetInnerHTML={{
          __html: `
            var ua = window.navigator.userAgent;
            var msie = ua.indexOf('MSIE ');
            var trident = ua.indexOf('Trident/');
            var temp = msie > 0 || trident > 0;
            if (temp) {
              var el = document.getElementById('ie-container');
              if (el) {
                el.style.display = 'block';
                document.body.style.overflow = 'hidden';
              }
            }
          `,
        }} />
        <Scripts />
        <script
          dangerouslySetInnerHTML={{
            __html: `
              window.g_config = {
                bizCode: 'gjbt',
                region: true,
                user: true,
                footer: true,
                webww: false,
                popSDK: true,
              }
              var g_config = window.g_config || {};
              g_config.jstracker2 = g_config.jstracker2 || {};
              g_config.jstracker2.pid = '34144-tracker';
            `,
          }}
        />
        <script
          src="//o.alicdn.com/tbhome/tbnav/index.js"
          crossOrigin="anonymous"
        />
        <script
          src="//g.alicdn.com/jstracker/sdk-assests/5.1.27/index.js"
          crossOrigin="anonymous"
        />
        <Logger />
        <script
          type="text/javascript"
          defer
          dangerouslySetInnerHTML={{
            __html: `
              try {
                if(window.securitySDK) {
                  const SecuritySDK = window.securitySDK.securitySDK;
                  const PiskMode = window.securitySDK.PiskMode;
                  const BizCode = window.securitySDK.BizCode;
                  if(SecuritySDK && PiskMode && BizCode){
                    const sdk = new SecuritySDK({ 
                      piskMode: PiskMode.ALL,
                      bizCode: BizCode.Venue
                    });
                    sdk.start();
                  }
                  // 安全--开启用户行为监测
                  window.securitySDK.startUBCollect?.();
                }
              } catch(e) {}
            `
          }}
        />
      </body>
    </html>
  );
}
