import { useState, useEffect, useCallback, useRef } from 'react';
import { reportCustom, parseUrlParams, generateUUID, mtopRequest } from '@ali/pcom-tbpc-venue-utils';
import {
  BaseItem,
  TabData,
  TabState,
  FetchParams,
  UseHomeDataReturn
} from '@/interface/homeData';
import { generateTtid, isPre, spmB } from '@/utils';

// 常量
const PAGE_SIZE = 18;
const RES_ID = '37455915';

let _guobu_init_request = 0;

/**
 * 构建请求参数
 */
const buildRequestParams = (
  tabId: string,
  pageNo: number,
  address: IDeliveryAddress,
  secondaryTabId?: string | string[],
  needFloatContents = false
): FetchParams => {
  window.__pvuuid ??= `v1-${generateUUID()}`;
  // const pvuuid = 'v1-655ac4b0-d0c1-437b-adb2-c098ffaadd1e-1746607334279'
  const pvuuid = window.__pvuuid;
  _guobu_init_request++;
  return {
    params: JSON.stringify({
      ...(window.__tbpc_gjbt_feeds_query_params || {}),
      bizId: 443,
      resId: RES_ID,
      app: 'pc',
      divisionCode: window.__tbpc_gjbt_feeds_query_params?.provinceCode,
      isOversea: window.__tbpc_gjbt_feeds_query_params?.isOversea ?? false,
      districtDivisionCode: address?.divisionCode,
      addressId: address?.deliverId,
      cdnOnly: false,
      useNewCardConfig: 1,
      _pvuuid: pvuuid,
      pvuuid,
      needNav: needFloatContents,
      pageSize: PAGE_SIZE,
      category: tabId ?? 'all',
      bizFilter: secondaryTabId ? encodeURIComponent(
        Array.isArray(secondaryTabId)
          ? secondaryTabId.join(',')
          : secondaryTabId
      ) : 'all',
      abConfigJsonStr: JSON.stringify({
        // 运营要求主图
        imageUrl: 'mainPictUrl'
      }),
      pageNo,
      fromSource: spmB,
      urlParams: '{}',
      fromItemId: '',
      flowSource: 'gbChannel',
      itemIds: _guobu_init_request <=1 ? (parseUrlParams()?.itemId || '') : '',
    })
  };
};

/**
 * 创建默认的标签页状态
 */
const createDefaultTabState = <T extends BaseItem>(addressId?: string | number): TabState<T> => {
  return {
    data: null,
    pageNo: -1,
    items: [],
    hasMore: true,
    initialized: false,
    loading: false,
    addressId // 添加地址ID
  };
};

export const useHomeData = <T extends BaseItem = BaseItem>(
  address: IDeliveryAddress,
  secondaryTabId?: string | string[] // 修改为支持字符串数组
): UseHomeDataReturn<T> => {
  // 状态管理
  const [data, setData] = useState<TabData<T> | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [tabStates, setTabStates] = useState<Record<string, TabState<T>>>({});

  // Refs
  const activeTabRef = useRef<string | null>(null);
  const secondaryTabIdRef = useRef<string | string[] | undefined>(secondaryTabId);
  secondaryTabIdRef.current = secondaryTabId;
  const tabStatesRef = useRef(tabStates);
  tabStatesRef.current = tabStates;
  const loadMoreTimeoutRef = useRef<number | null>(null);
  const addressRef = useRef(address);
  addressRef.current = address;
  const requestLockRef = useRef<Record<string, boolean>>({});
  // 添加请求队列管理
  const pendingRequestRef = useRef<Promise<void> | null>(null);
  const requestAbortControllerRef = useRef<AbortController | null>(null);

  /**
   * 更新标签页状态
   */
  const updateTabState = useCallback((tabId: string, updates: Partial<TabState<T>>) => {
    setTabStates(prev => ({
      ...prev,
      [tabId]: {
        ...(prev[tabId] || createDefaultTabState<T>(addressRef.current?.deliverId)),
        ...updates
      }
    }));
  }, []);

  /**
   * 处理请求成功的响应
   */
  const handleRequestSuccess = useCallback((
    tabId: string,
    requestTabId: string,
    pageNo: number,
    response: any,
    needFloatContents = false
  ) => {
    const newData = response.data?.[0] || response.data;
    const newItems = (newData?.items || []) as T[];
    // 如果是第一页且返回的items为空，则认为没有更多数据
    const hasMoreData = pageNo === 0 && newItems.length === 0
      ? false
      // hasMore 不可信
      : newData?.hasMore === true && (newItems.length >= 1);

    // 更新标签页状态
    setTabStates(prev => {
      const prevTabState = prev[tabId] || createDefaultTabState<T>();

      const updatedItems = pageNo === 0
        ? newItems
        : [...prevTabState.items, ...newItems];

      // 处理 floatContents，根据 needFloatContents 决定是否使用新数据
      let floatContents: any[];
      if (needFloatContents && (response.floatContents || newData?.floatContents)) {
        // 如果需要获取 floatContents 且响应中有数据，则使用新数据
        floatContents = (response.floatContents || newData?.floatContents).filter((item: any) => !item?.hidden);
      } else {
        // 否则保持之前的数据，或者从其他地方获取
        floatContents = prevTabState.floatContents ||
          data?.floatContents ||
          // 从其他tab中获取 floatContents
          Object.values(tabStatesRef.current).find(state => state?.floatContents)?.floatContents ||
          [];
      }

      // 处理 bizFilters，可能来自 response 或 newData
      const bizFilters = response.bizFilters || newData?.bizFilters
        ? response.bizFilters || newData?.bizFilters
        : prevTabState.bizFilters;

      const updatedData = {
        ...newData,
        items: updatedItems,
        floatContents,
        bizFilters,
      };

      // 只有当前活动的标签页仍然是请求的标签页时，才更新UI数据
      if (activeTabRef.current === requestTabId) {
        setData(updatedData);
        setHasMore(hasMoreData);
      }

      return {
        ...prev,
        [tabId]: {
          data: updatedData,
          pageNo,
          items: updatedItems,
          hasMore: hasMoreData,
          initialized: true,
          loading: false,
          floatContents,
          bizFilters,
          addressId: addressRef.current?.deliverId // 保存当前地址ID
        }
      };
    });
  }, []);



  /**
   * 等待之前的请求完成
   */
  const waitForPendingRequest = useCallback(async () => {
    if (pendingRequestRef.current) {
      try {
        await pendingRequestRef.current;
      } catch (error) {
        // 忽略之前请求的错误，继续执行新请求
        // console.log('Previous request failed, continuing with new request');
      }
    }
  }, []);

  /**
   * 取消当前请求
   */
  const cancelCurrentRequest = useCallback(() => {
    if (requestAbortControllerRef.current) {
      requestAbortControllerRef.current.abort();
      requestAbortControllerRef.current = null;
    }
  }, []);

  /**
   * 获取标签页数据
   */
  const fetchTabData = useCallback(async (
    tabId: string,
    pageNo = 0,
    forceRefresh = false,
    needFloatContents = false
  ): Promise<void> => {
    if (!tabId) return;
    if (!address?.deliverId) return;

    // 生成请求的唯一标识
    const requestKey = `${tabId}-${pageNo}-${secondaryTabIdRef.current || 'all'}`;

    // 如果相同的请求正在进行中，则跳过
    if (requestLockRef.current[requestKey]) {
      return;
    }

    // 记录请求时的标签页ID，用于后续检查
    const requestTabId = tabId;
    activeTabRef.current = tabId;

    // 如果是强制刷新，取消当前请求
    if (forceRefresh && pageNo === 0) {
      cancelCurrentRequest();
    } else {
      // 等待之前的请求完成
      await waitForPendingRequest();
    }

    // 不使用缓存，每次都重新请求数据

    // 创建新的AbortController
    const abortController = new AbortController();
    requestAbortControllerRef.current = abortController;

    // 创建请求Promise并存储
    const requestPromise = (async () => {
      try {
        // 设置请求锁
        requestLockRef.current[requestKey] = true;

        if (activeTabRef.current === requestTabId) {
          setLoading(true);
        }
        updateTabState(tabId, {
          loading: true,
          secondaryTabId: secondaryTabIdRef.current // 记录当前二级标签页ID
        });
        setError(null);

        const params = buildRequestParams(tabId, pageNo, address, secondaryTabIdRef.current, needFloatContents);

        // 检查请求是否已被取消
        if (abortController.signal.aborted) {
          throw new Error('Request was cancelled');
        }

        const mtopResponse = await mtopRequest({
          api: 'mtop.tmall.kangaroo.core.service.route.AldLampServiceFixedResV2',
          v: '1.0',
          data: params,
          ttid: generateTtid(),
          type: 'POST',
          // 注意：mtopRequest可能不支持AbortSignal，这里先添加检查
        });
        // 再次检查请求是否已被取消
        if (abortController.signal.aborted) {
          throw new Error('Request was cancelled');
        }

        const response = mtopResponse?.resultValue?.[RES_ID];
        if (response?.msgCode === '2201' || response?.msgInfo === 'SolutionNeedLogin') {
          window.location.href = `https://login.taobao.com/havanaone/login/login.htm?bizName=taobao&redirectURL=${encodeURIComponent(window.location.href)}`;
          return;
        }

        // 处理接口返回错误状态的情况
        if (response && response.success === false) {
          throw new Error(JSON.stringify(response));
        }

        if (!response || !response?.data?.length) {
          // 检查所有 tab 是否有 floatContents
          const hasFloatContents = Object.values(tabStatesRef.current).some(state => state?.floatContents);
          if (!hasFloatContents) {
            throw new Error(typeof mtopResponse === 'object' ? JSON.stringify({ ...mtopResponse, isPre }) : '');
          }
        }

        // 最后检查请求是否已被取消
        if (abortController.signal.aborted) {
          throw new Error('Request was cancelled');
        }

        handleRequestSuccess(tabId, requestTabId, pageNo, response, needFloatContents);
      } catch (err) {
        const error = err instanceof Error ? err : new Error('Unknown error');

        // 如果是请求被取消，不需要更新错误状态
        if (error.message === 'Request was cancelled') {
          // console.log('Request was cancelled for tab:', tabId);
          return; // 直接返回，不更新任何状态
        }

        // 只有当前活动的标签页仍然是请求的标签页时，才更新错误状态
        if (activeTabRef.current === requestTabId) {
          setError(error);
          setHasMore(false);
          reportCustom({
            // 刷新后依然失败，需要重点监控
            code: forceRefresh ? 'tbpc_guobu_refresh_exception_ui' : 'tbpc_guobu_exception_ui',
            message: error?.message || '',
            success: false,
            sampling: 1,
          });
        }
        updateTabState(tabId, {
          loading: false,
          hasMore: false,
          error
        });
      } finally {
        // 释放请求锁
        delete requestLockRef.current[requestKey];

        // 只有在请求没有被取消的情况下才更新loading状态
        if (!abortController.signal.aborted) {
          if (activeTabRef.current === requestTabId) {
            setLoading(false);
          }
          updateTabState(tabId, { loading: false });
        }

        // 清理AbortController引用
        if (requestAbortControllerRef.current === abortController) {
          requestAbortControllerRef.current = null;
        }
      }
    })();
    // 存储当前请求Promise
    pendingRequestRef.current = requestPromise;

    // 等待请求完成
    await requestPromise;
  }, [address, updateTabState, handleRequestSuccess, waitForPendingRequest, cancelCurrentRequest]);


  /**
   * 加载更多数据
   */
  const loadMore = useCallback((tabId?: string): void => {
    const activeTab = tabId || activeTabRef.current;
    if (!activeTab) return;

    const currentTabState = tabStatesRef.current[activeTab];
    if (currentTabState?.loading || !currentTabState?.hasMore) return;

    const nextPage = (currentTabState?.pageNo ?? -1) + 1;
    fetchTabData(activeTab, nextPage);
  }, [fetchTabData]);

  /**
   * 刷新数据
   */
  const refresh = useCallback(async (tabId: string, newSecondaryTabId?: string | string[]): Promise<void> => {
    if (!tabId) return;

    // 如果传入了新的二级tab ID，临时更新 ref
    const originalSecondaryTabId = secondaryTabIdRef.current;
    if (newSecondaryTabId !== undefined) {
      secondaryTabIdRef.current = newSecondaryTabId;
    }

    // 尝试从更多可能的来源获取 floatContents
    let currentFloatContents: any[] | null = null;

    // 1. 从当前数据中获取
    if (data?.floatContents) {
      currentFloatContents = data.floatContents;
    }
    // 2. 从当前tab状态中获取
    else if (tabStatesRef.current[tabId]?.floatContents) {
      currentFloatContents = tabStatesRef.current[tabId].floatContents;
    }
    // 3. 从当前tab的data中获取
    else if (tabStatesRef.current[tabId]?.data?.floatContents) {
      currentFloatContents = tabStatesRef.current[tabId].data.floatContents;
    }
    // 4. 从其他tab中获取（如果当前tab没有）
    else {
      // 遍历所有tab，找到第一个有floatContents的
      for (const [key, state] of Object.entries(tabStatesRef.current)) {
        if (key !== tabId) {
          if (state.floatContents) {
            currentFloatContents = state.floatContents;
            break;
          } else if (state.data?.floatContents) {
            currentFloatContents = state.data.floatContents;
            break;
          }
        }
      }
    }

    // 只清空商品数据，保留 tabs 数据
    setData(prev => prev ? {
      ...prev,
      items: [],
      floatContents: currentFloatContents || prev.floatContents // 优先使用找到的，否则保留原有的
    } : null);
    setHasMore(true);
    setError(null);

    // 重置当前 tab 状态，但保留 floatContents
    updateTabState(tabId, {
      initialized: false,
      hasMore: true,
      pageNo: -1,
      items: [],
      data: currentFloatContents ? {
        items: [],
        floatContents: currentFloatContents
      } : null,
      addressId: address?.deliverId // 设置当前地址ID
    });

    try {
      return await fetchTabData(tabId, 0, true, true);
    } finally {
      // 恢复原来的二级tab ID
      if (newSecondaryTabId !== undefined) {
        secondaryTabIdRef.current = originalSecondaryTabId;
      }
    }
  }, [fetchTabData, updateTabState, address?.deliverId]);



  /**
   * 处理无缓存数据的情况
   */
  const handleNoCache = useCallback((tabId: string) => {
    // 没有缓存数据，需要加载
    setData(prev => {
      // 如果当前活动的标签页已经变化，不更新数据
      if (activeTabRef.current !== tabId) return prev;
      return prev ? {
        ...prev,
        items: [],
        hasMore: true
      } : null;
    });
    setLoading(true);

    // 使用setTimeout延迟加载，确保UI有时间响应
    setTimeout(() => {
      // 只有当前活动的标签页仍然是这个标签页时，才加载数据
      if (activeTabRef.current === tabId) {
        fetchTabData(tabId, 0, false, false);
      }
    }, 0);
  }, [fetchTabData]);

  /**
   * 切换标签页
   */
  const switchTab = useCallback((tabId: string): void => {
    if (activeTabRef.current === tabId) return;

    // 切换到新标签页
    activeTabRef.current = tabId;

    // 取消可能的加载更多定时器
    if (loadMoreTimeoutRef.current) {
      clearTimeout(loadMoreTimeoutRef.current);
      loadMoreTimeoutRef.current = null;
    }

    window.scrollTo(0, 0);

    // 不使用缓存数据，每次切换都重新加载
    handleNoCache(tabId);
  }, [handleNoCache]);

  /**
   * 重试
   */
  const retry = useCallback(() => {
    if (activeTabRef.current) {
      const currentTabState = tabStatesRef.current[activeTabRef.current];
      const currentFloatContents = currentTabState?.floatContents;

      // 重置状态但保留 floatContents
      updateTabState(activeTabRef.current, {
        initialized: false,
        hasMore: true,
        pageNo: -1,
        items: [],
        data: currentFloatContents ? { floatContents: currentFloatContents, items: [] } : null,
        error: null
      });

      // 清空商品数据但保留 floatContents
      setData(prev => prev ? {
        ...prev,
        items: [],
        hasMore: true
      } : null);
      setError(null);
      setHasMore(true);

      // 重新请求数据
      return setTimeout(() => {
        if (activeTabRef.current) {
          fetchTabData(activeTabRef.current, 0, true, true);
        }
      }, 500);
    }
    return Promise.resolve(); // 返回一个空的Promise以解决警告
  }, [fetchTabData, error, updateTabState]);

  // 监听二级标签页变化，重新请求数据
  useEffect(() => {
    if (activeTabRef.current && address?.deliverId) {
      const currentTabState = tabStatesRef.current[activeTabRef.current];
      // 如果当前标签页已经初始化过，且二级标签页发生了变化，则重新请求数据
      const currentSecondaryTabId = currentTabState?.secondaryTabId;
      const isSecondaryTabChanged = Array.isArray(currentSecondaryTabId) && Array.isArray(secondaryTabId)
        ? JSON.stringify(currentSecondaryTabId.sort()) !== JSON.stringify([...secondaryTabId].sort())
        : currentSecondaryTabId !== secondaryTabId;

      if (currentTabState?.initialized && isSecondaryTabChanged) {
        // 立即设置loading状态，确保用户能看到loading效果
        setLoading(true);
        // 使用setTimeout确保loading状态至少持续一段时间
        setTimeout(() => {
          fetchTabData(activeTabRef.current!, 0, true);
        }, 100);
      }
    }
  }, [secondaryTabId, fetchTabData, address?.deliverId]);

  // 确保初始加载后有足够的数据显示至少4行卡片
  useEffect(() => {
    if (data && !loading && data.items && activeTabRef.current) {
      // 假设每行最多显示6个卡片，那么3行需要18个
      const minItemsNeeded = 18;

      // 如果当前数据不足以显示4行，且还有更多数据可加载，则自动加载更多
      // 添加检查：确保items数组不为空才继续加载
      if (data.items.length > 0 && data.items.length < minItemsNeeded && hasMore) {
        // 使用setTimeout避免可能的无限循环
        loadMoreTimeoutRef.current = window.setTimeout(() => {
          activeTabRef.current && loadMore(activeTabRef.current);
        }, 100);
      }
    }
  }, [data, loading, hasMore, loadMore]);

  // 提供设置loading状态的方法
  const setLoadingState = useCallback((loadingState: boolean) => {
    setLoading(loadingState);
  }, []);

  return {
    data,
    loading,
    error,
    hasMore,
    fetchTabData,
    switchTab,
    refresh,
    loadMore,
    currentTabState: activeTabRef.current ? tabStatesRef.current[activeTabRef.current] : null,
    retry,
    setLoading: setLoadingState,
    cancelCurrentRequest
  };
};

