import { generateTtid } from "@/utils";
import { mtopRequest, reportCustom } from "@ali/pcom-tbpc-venue-utils";
import { Modal } from "antd";
import { useEffect, useState } from "react";

/**
 * 初始化时不传divisionCode，通过该接口拿到用户默认收货地址（无默认会自动拿第一个地址）和猫超zoneId
 * 切换地址时，通过该接口拿到猫超zoneId，便于请求feeds做猫超价格补全
 * @param divisionCode 地址编码
 * @returns { IDeliveryAddress }
 */
export async function getTargetAddressInfo(divisionCode?: string) {
  return mtopRequest({
    api: 'mtop.tmall.industry.ofn.gov.subsidy.minisite.home',
    data: {
      divisionCode,
    },
    ttid: generateTtid(),
  }).then(res => {
    const data = res?.resultData;
    // if (!data?.chaoshiZoneId) {
    //   throw res;
    // }
    window.__tbpc_gjbt_feeds_query_params = {
      chaoshiZoneId: data?.chaoshiZoneId,
      deliverId: data?.deliverId,
      divisionCode: data?.divisionCode,
      isOversea: data?.isOversea,
      provinceCode: data?.provinceCode,
      provinceDivisionIcTag: data?.provinceDivisionIcTag,
    };
  }).catch((e) => {
    window.__tbpc_gjbt_feeds_query_params ??= {
      // chaoshiZoneId: 107,
    };
    reportCustom({
      code: 'tbpc_guobu_minisitehome_error',
      message: typeof e === 'object' ? JSON.stringify(e) : e,
      success: false,
      sampling: 1,
    });
  });
}

export default (func: (address: IDeliveryAddress) => void): IDeliveryAddress[] => {
  const [list, setList] = useState<IDeliveryAddress[]>([]);

  const queryDeliveryAddressList = () => {
    mtopRequest({
      api: 'com.taobao.mtop.deliver.getaddresslist',
      v: '3.0',
      data: {},
      ttid: generateTtid(),
    }).then((res: { addressList: IDeliveryAddress[] }) => {
      if (!res?.addressList?.length) {
        Modal.warning({
          title: '请添加收货地址',
          content: '查询到您的账户尚未设置任何收货地址，请先添加收货地址，我们才能向您准确推荐可购买的国补优惠商品哦~',
          okText: '去添加',
          wrapClassName: 'guobu-modal',
          onOk: () => {
            window.location.href = 'https://member1.taobao.com/member/fresh/deliver_address.htm';
          },
        });
        return;
      }
      setList(res.addressList);
      // 查到与接口返回的用户默认收货地址（无默认就是第一个）匹配的地址
      const targetAddress = res.addressList.find(item => item.deliverId === window.__tbpc_gjbt_feeds_query_params?.deliverId);
      func(targetAddress || res.addressList[0]);
    }).catch(e => {
      if (e?.ret?.[0]?.indexOf('FAIL_SYS_SESSION_EXPIRED') > -1) {
        window.location.href = `https://login.taobao.com/havanaone/login/login.htm?bizName=taobao&redirectURL=${encodeURIComponent(window.location.href)}`;
      } else {
        reportCustom({
          code: 'tbpc_guobu_getaddresslist_error',
          message: typeof e === 'object' ? JSON.stringify(e) : e,
          success: false,
          sampling: 1,
        });
      }
    });
  }

  useEffect(() => {
    if (window.__tbpc_gjbt_feeds_query_params?.deliverId) {
      queryDeliveryAddressList();
    } else {
      getTargetAddressInfo().then(queryDeliveryAddressList);
    }
  }, []);
  return list;
}