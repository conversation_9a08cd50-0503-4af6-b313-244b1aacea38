import { useEffect, useCallback, useRef } from 'react';

export const useInfiniteScroll = (
  containerRef: React.RefObject<HTMLElement>,
  onLoadMore: () => void,
  options: {
    threshold?: number;
    disabled?: boolean;
  } = {}
) => {
  // 增加默认阈值到 800，提前加载更多内容
  const { threshold = 800, disabled = false } = options;
  const loadingRef = useRef(false);
  const lastScrollTop = useRef(0);

  const handleScroll = useCallback(() => {
    if (disabled || loadingRef.current) return;

    const scrollHeight = document.documentElement.scrollHeight;
    const scrollTop = window.scrollY || document.documentElement.scrollTop;
    const clientHeight = window.innerHeight;
    const remainingDistance = scrollHeight - (scrollTop + clientHeight);
    const isScrollingDown = scrollTop > lastScrollTop.current;

    // 更新上次滚动位置
    lastScrollTop.current = scrollTop;

    // 当向下滚动且剩余距离小于阈值时触发加载
    if (isScrollingDown && remainingDistance <= threshold) {
      loadingRef.current = true;
      onLoadMore();
      
      // 较短的重置时间以支持快速加载
      setTimeout(() => {
        loadingRef.current = false;
      }, 200);
    }
  }, [onLoadMore, threshold, disabled]);

  useEffect(() => {
    const scrollElement = window;
    let rafId: number;

    // 使用 requestAnimationFrame 进行节流
    const optimizedHandler = () => {
      cancelAnimationFrame(rafId);
      rafId = requestAnimationFrame(() => {
        handleScroll();
      });
    };

    scrollElement.addEventListener('scroll', optimizedHandler, { passive: true });
    
    // 初始检查是否需要加载
    handleScroll();
    
    return () => {
      scrollElement.removeEventListener('scroll', optimizedHandler);
      cancelAnimationFrame(rafId);
    };
  }, [handleScroll]);

  // 添加一个定期检查的机制，以防某些情况下滚动事件没有触发
  useEffect(() => {
    if (disabled) return;

    const intervalId = setInterval(() => {
      handleScroll();
    }, 1000);

    return () => clearInterval(intervalId);
  }, [handleScroll, disabled]);
};
