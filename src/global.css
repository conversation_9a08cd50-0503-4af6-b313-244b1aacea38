/**
 * The global.css defines the style of the entire project,
 * ice.js will globally import this file by default.
 */
:root {
  --primary: #03a848;
  --bg-primary: white;
  --font-family: system-ui, -apple-system, Segoe UI, Roboto, Ubuntu, Cantarell, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji, BlinkMacSystemFont, Helvetica Neue, Arial, PingFang SC, PingFang TC, PingFang HK, Microsoft Yahei, Microsoft JhengHei;
}

body {
  margin: 0;
  font-family: var(--font-family);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 设置标题字体 */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-family);
}

/* 设置按钮字体 */
button, input, select, textarea, span {
  font-family: var(--font-family);
}

#ie-container {
  position: fixed;
  z-index: 99999999;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: #fff;
  display: block;
}
#ie-container :global(body) {
  overflow: hidden;
}
.ie-content-container {
  width: 800px;
  margin: 200px auto 0;
}

.ie-left-container {
  float: left;
}

.ie-left-pic {
  width: 126px;
  height: 60px;
}

.ie-left-pic-img {
  width: 126px;
  margin-top: 13px;
}

.ie-left-title {
  margin-top: 24px;
  color: #11192d;
  font-size: 24px;
  line-height: 24px;
}

.ie-button-container {
  margin-top: 24px;
}

.ie-button-main {
  width: 112px;
  height: 48px;
  cursor: pointer;
  color: #fff;
  border-radius: 8px;
  background-color: #ff5000;
  font-size: 16px;
  line-height: 48px;
  text-align: center;
}

.ie-right-container {
  float: right;
}

.ie-right-pic {
  width: 160px;
  height: 160px;
}
