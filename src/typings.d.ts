/// <reference types="@ice/app/types" />

declare module '*.less' {
  const classes: { [key: string]: string };
  export default classes;
}

interface Window {
  $data: any;
  $system: {
    env: 'pre' | 'production';
    [x: string]: any;
  };
  lib: any;
  __tbpc_venue_useAddCard: boolean;
  __tbpc_gjbt_schema_config_data: any;
  __pvuuid: string;
  __tbpc_gjbt_feeds_query_params: Record<string, any>;
}
