{"seed": {}, "pageInfo": {"components": [{"settings": {}, "componentId": 153645, "hidden": "false", "fullName": "@ali/pegasus-project--tbpc-guobu-channel--guobu-config", "uuid": "3784477370", "limitConfig": {}, "version": "1.0.0", "componentVersionId": 1356776, "name": "pegasus-project--tbpc-guobu-channel--guobu-config"}], "keywords": "", "spma": "a21bo", "seaConfig": "", "spmb": "30161185", "description": "", "title": "国补频道", "templateId": 0, "path": "/tbhome/tbpc-venue/gjbt-test", "activityId": 0, "offlineUrl": "https://huodong.taobao.com", "pageType": 10, "customConfig": {"productType": 10, "forceTerminal": "pc", "url": "https://huodong.taobao.com/wow/z/tbhome/tbpc-venue/gjbt-test", "pha": false, "autoOffline": true, "path": "banff-pages/pegasus-project--tbpc-guobu-channel/1.0.0/pages/index"}, "offlineTime": "3127-04-23 15:17:12", "id": 3911338, "publishTime": "2025-04-25 18:00:56", "testPage": 1, "schemaVersion": "445b7996-6013-4044-9bff-b8bc48d0744d", "terminal": "pc", "mobileNative": 2, "solutionConfig": {"solutionAndConfigPath": "banff-solution-config/26279/index.xtpl"}, "createPlatform": "zebra", "name": "国补频道", "style": "<style></style>", "plugins": {}}, "static": {"3784477370": {"resConfig": {"headerResImg": "https://img.alicdn.com/imgextra/i2/O1CN01136JP01uHYCN7BsvB_!!6000000006012-2-tps-512-160.png", "headerResUrl": "https://www.taobao.com"}, "funcConfig": {"canAddCard": true, "distinctId": "17455753056590001", "dataSetId": 44390525, "currentAldResId": "37560226", "id": "17455753056590001", "__pos__": 1, "__track__": "37560226.37560226.48559502.2.1"}, "searchConfig": {"searchPlaceholder": "冰箱,洗衣机,空调,家电", "distinctId": "17455752445400001", "dataSetId": 44397341, "icString": "service:gjbt", "currentAldResId": "37551458", "id": "17455752445400001", "__pos__": 1, "__track__": "37551458.37551458.48550678.2.1"}}, "ald_data": true}, "page": {"3784477370": {"funcConfig": {"canAddCard": true, "distinctId": "17455753056590001", "dataSetId": 44390525, "currentAldResId": "37560226", "id": "17455753056590001", "__pos__": 1, "__track__": "37560226.37560226.48559502.2.1"}, "searchConfig": {"searchPlaceholder": "冰箱,洗衣机,空调,家电", "distinctId": "17455752445400001", "dataSetId": 44397341, "icString": "service:gjbt", "currentAldResId": "37551458", "id": "17455752445400001", "__pos__": 1, "__track__": "37551458.37551458.48550678.2.1"}}, "ald_data": true}, "$resources": {"3784477370": {"resConfig": {"__data_default": {"length": 20}, "__data_from_static": "true", "__data_param": {"appId": "37552450", "bizId": 100, "terminalType": 0}, "__data_source": "ald-lamp", "__data_type": "jsonp", "__data_url": "mtop.tmall.kangaroo.core.service.route.AldLampService"}, "funcConfig": {"__data_default": {"length": 20}, "__data_from_static": "true", "__data_param": {"appId": "37560226", "bizId": 100, "terminalType": 0}, "__data_source": "ald-lamp", "__data_type": "jsonp", "__data_url": "mtop.tmall.kangaroo.core.service.route.AldLampService"}, "searchConfig": {"__data_default": {"length": 20}, "__data_from_static": "true", "__data_param": {"appId": "37551458", "bizId": 100, "terminalType": 0}, "__data_source": "ald-lamp", "__data_type": "jsonp", "__data_url": "mtop.tmall.kangaroo.core.service.route.AldLampService"}}, "ald_data": true}}