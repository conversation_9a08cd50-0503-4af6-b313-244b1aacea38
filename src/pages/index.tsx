import { useState, useCallback, useEffect, useMemo } from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import Header from '@/components/Header';
import Feeds from '@/components/Feeds';
import DeliveryAddress from '@/components/DeliveryAddress';
import Search from '@/components/Search';
import { useHomeData } from '@/hooks/useHomeData';
import styles from './index.module.less';
import { AdaptLayout } from '@ali/pcom-tbpc-mod-container';
import ErrorFallback from '@/components/ErrorFallback';
import { definePageConfig } from 'ice';
import { getSchemaDataConfig, isLogin, spmB } from '@/utils';
import BuyPopup from '@ali/alimod-tbpc-venue-buy-ice/esm/pc';
import { setRecordEventConfig, addRecordEventCommonParams } from '@ali/onex-bi'
import { isTrue, reportCustom } from '@ali/pcom-tbpc-venue-utils';
import HeaderResBanner from '@/components/HeaderResBanner';
import { Spin } from 'antd';
import './reset.less';

interface HomePageProps {
  initialTabId?: string;
}

export default function HomePage({ initialTabId = 'all' }: HomePageProps) {
  const [curAddress, setAddress] = useState<IDeliveryAddress>({} as IDeliveryAddress);
  const [curId, setCurId] = useState<string>(initialTabId);
  const [curSecondaryId, setCurSecondaryId] = useState<string[]>(['all']);
  const [showSpin, setShowSpin] = useState(false);

  const _canAddCard = useMemo(() => {
    const { canAddCard } = getSchemaDataConfig('funcConfig')?.[0] || {};
    window.__tbpc_venue_useAddCard = !!isTrue(canAddCard);
    return canAddCard;
  }, []);

  const {
    data,
    loading,
    error,
    retry,
    hasMore,
    loadMore,
    switchTab,
    refresh,
    cancelCurrentRequest
  } = useHomeData(curAddress, curSecondaryId);

  // 处理地址变更，重置为第一个tab
  const handleAddressChange = useCallback((address: IDeliveryAddress) => {
    setAddress(address);
    setCurId('all');
    setCurSecondaryId(['all']);
  }, []);

  // 处理二级tab切换
  const handleSecondaryTabChange = useCallback((secondaryId: string | string[]) => {
    // 取消当前请求
    cancelCurrentRequest();
    const newSecondaryId = Array.isArray(secondaryId) ? secondaryId : [secondaryId];
    // 先更新状态
    setCurSecondaryId(newSecondaryId);
    // 使用新的二级tab ID进行刷新
    refresh(curId, newSecondaryId);
  }, [cancelCurrentRequest, refresh, curId]);

  useEffect(() => {
    // 埋点 SDK 配置
    setRecordEventConfig({
      tenantId: 'oldForNew',
      productId: 'home-pc',
      requiredCommonParams: ['provinceCode'],
    });

    // 强登校验
    if (!isLogin()) {
      window.location.href = `https://login.taobao.com/havanaone/login/login.htm?bizName=taobao&redirectURL=${encodeURIComponent(window.location.href)}`;
    }
  }, []);

  useEffect(() => {
    if (curAddress?.divisionCode) {
      addRecordEventCommonParams({
        provinceCode: curAddress.divisionCode.slice(0, 2) + '0000',
      });
    }
  }, [curAddress]);

  // 仅监听地址ID变化，避免死循环
  useEffect(() => {
    if (curAddress?.deliverId) {
      setShowSpin(true);
      // 地址变化时，需要获取类目tab数据，所以使用refresh
      refresh('all').finally(() => {
        setShowSpin(false);
      });
    }
  }, [curAddress?.deliverId, refresh]);

  // 单独监听数据变化，处理tab切换
  useEffect(() => {
    // 只有在有数据且不在加载状态时才处理
    if (data?.floatContents?.length && data.floatContents.length > 0 && !loading && curId === 'all') {
      const firstTabId = data.floatContents[0]?.tabId;
      if (firstTabId && firstTabId !== 'all') {
        setCurId(firstTabId);
        switchTab(firstTabId);
      }
    }
  }, [data?.floatContents, loading, curId, switchTab]);



  const handleTabChange = useCallback((tabId: string) => {
    setCurId(tabId);
    setCurSecondaryId(['all']);
    switchTab(tabId);
  }, [switchTab]);

  const handleLoadMore = useCallback(() => {
    if (!curId) return;
    requestAnimationFrame(() => {
      loadMore(curId);
    });
  }, [curId, loadMore]);

  const currentItemslist = useMemo(() => data?.items || [], [data?.items]);
  const tabsList = useMemo(() => {
    return data?.floatContents?.map((item: any) => ({
      key: item.tabId,
      label: item.tabName,
    })) || [];
  }, [data?.floatContents]);

  // 二级tab数据
  const secondaryTabsList = useMemo(() => {
    // 获取配置的二级tab IDs
    const { secondaryTabTypes } = getSchemaDataConfig('tabConfig')?.[0] || {};
    const configuredTypes = secondaryTabTypes ? secondaryTabTypes.split(/[,，]/).map((type: string) => type?.trim()) : [];

    // 过滤bizFilters，只保留配置中指定的tab
    const bizFilterTabs = data?.bizFilters
      ?.filter(item => configuredTypes?.includes(item?.tabType))
      ?.map((item: any) => ({
        key: item?.tabId,
        label: item?.tabName,
        ...item,
      })) || [];

    // 手动添加"全部"选项到第一位
    return [
      { key: 'all', label: '全部' },
      ...bizFilterTabs
    ];
  }, [data?.bizFilters]);

  // 初始化二级tab，默认设置为"all"
  useEffect(() => {
    if (secondaryTabsList.length > 0 && !curSecondaryId) {
      setCurSecondaryId(['all']);
    }
  }, [secondaryTabsList, curSecondaryId]);

  // 使用 useMemo 缓存内容，避免不必要的重新渲染
  const content = useMemo(() => {
    // 只有在确实有错误且不在加载状态且没有任何数据时才显示错误页面
    if (error && !loading && (!data?.floatContents || data.floatContents.length === 0)) {
      return <ErrorFallback onRetry={retry} />;
    }

    return (
      <>
        <Header
          curId={curId}
          tabsList={tabsList}
          onChange={handleTabChange}
          loading={!data?.floatContents}
          curSecondaryId={curSecondaryId}
          secondaryTabsList={secondaryTabsList}
          onSecondaryChange={handleSecondaryTabChange}
          secondaryLoading={!data?.bizFilters}

        />
        <Feeds
          curId={curId}
          currentItemslist={currentItemslist}
          benefitDefaultText="火爆热卖中"
          loading={loading || !data?.floatContents}
          onLoadMore={handleLoadMore}
          hasMore={hasMore}
          divisionCode={curAddress?.divisionCode}
          onRefresh={() => refresh(curId)}
        />
      </>
    );
  }, [
    error,
    loading,
    curId,
    tabsList,
    handleTabChange,
    data,
    currentItemslist,
    handleLoadMore,
    hasMore,
    curAddress,
    retry,
    refresh,
    curSecondaryId,
    secondaryTabsList,
    handleSecondaryTabChange,
  ]);

  return (
    <ErrorBoundary FallbackComponent={({ error, resetErrorBoundary }) => {
      // 上报全局 tbpc_guobu_global_error
      reportCustom({
        code: 'tbpc_guobu_global_error',
        message: error?.message || '',
        success: false,
        sampling: 1,
      });

      return <ErrorFallback onRetry={() => {
        resetErrorBoundary();
        retry();
      }} />
    }
    }>
      <Spin spinning={showSpin} wrapperClassName={styles.spinWrapper}>
        <AdaptLayout className={styles.app}>
          <div className={styles.header}>
            <DeliveryAddress onSelect={handleAddressChange} />
            <Search />
            <HeaderResBanner />
          </div>
          {content}
          {_canAddCard && <BuyPopup />}
        </AdaptLayout>
      </Spin>
    </ErrorBoundary>
  );
}


export const pageConfig = definePageConfig(() => ({
  spm: {
    spmB,
    pageName: "gov-subsidy",
  },
  title: '国家补贴',
  meta: [
    {
      name: "keyword",
      value: '国家补贴,国补,淘宝补贴,淘宝国补,电脑版国补',
    },
    {
      name: "description",
      value: 'PC淘宝官方国家补贴频道，提供淘宝国补商品、补贴活动等，让淘宝国补更轻松',
    },
  ],
}));
